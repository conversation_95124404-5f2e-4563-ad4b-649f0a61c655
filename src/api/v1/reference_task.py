#!/usr/bin/env python3
"""
参考任务执行API接口

基于参考任务的动作列表，让模型参考执行新的用例
"""

import threading
import uuid
from typing import Dict

from fastapi import APIRouter
from loguru import logger
from pydantic import BaseModel, Field

from src.application.reference_task_application import reference_task_application
from src.domain.ui_task.mobile.android.adb_connection_manager import connect_device, disconnect_device
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler
from src.infra.utils import get_current_env
from src.schema import const
from src.schema.action_types import ExecutionStatus

router = APIRouter(tags=["reference-task"])

# 存储后台任务
background_tasks_registry: Dict[str, threading.Thread] = {}


class ReferenceTaskExecuteRequest(BaseModel):
    """参考任务执行请求"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="测试用例名称", min_length=1, max_length=500)
    task_description: str = Field(..., description="要执行的测试用例描述", min_length=1)
    reference_task_id: str = Field(..., description="参考任务ID，用于获取参考动作列表")
    app_id: str = Field(..., description="执行的软件app id", min_length=1)
    agent_type: str = Field(default="android", description="android | ios，默认 android")
    agent_config_id: str = Field(default="tt", description="agent 的开放 Prompt，默认传 'tt'")
    device: dict = Field(..., description="设备配置")
    
    # 可选的prompt参数化字段
    app_name: str = Field(None, description="应用名称，如：TT语音")
    app_description: str = Field(None, description="软件功能介绍内容，替换prompt中的软件功能介绍")
    ui_component_instructions: str = Field(None, description="UI组件操作说明，替换prompt中的UI组件操作说明")
    special_scenarios: str = Field(None, description="特殊场景处理说明，替换prompt中的特殊场景内容")
    
    # 应用重启控制字段
    is_restart: bool = Field(default=False, description="第一次执行前是否重启app，默认不重启")

    class Config:
        # 允许额外字段，提高兼容性
        extra = "ignore"


def execute_reference_task_background(
        task_id: str,
        request: ReferenceTaskExecuteRequest
):
    """
    后台执行参考任务
    
    Args:
        task_id: 任务ID
        request: 参考任务执行请求
    """
    try:
        logger.info(f"[{task_id}] 🚀 Starting reference task execution in background...")
        
        # 连接ADB设备
        device_id = request.device.get("android", {}).get("url")
        if not device_id:
            raise ValueError("Android device URL is required")
            
        connect_device(device_id, task_id)
        logger.info(f"[{task_id}] ✅ ADB device connected: {device_id}")
        
        # 执行参考任务
        result = reference_task_application.execute_reference_task(task_id, request)
        
        if result.get("success", False):
            logger.info(f"[{task_id}] ✅ Reference task execution completed successfully")
        else:
            logger.error(f"[{task_id}] ❌ Reference task execution failed: {result.get('message', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"[{task_id}] ❌ Reference task execution failed with exception: {str(e)}")
        # 使用异常处理器处理错误
        TaskExceptionHandler.handle_task_exception(task_id, e, "reference_task_execution")
        
    finally:
        # 清理ADB键盘设置
        try:
            reference_task_application.cleanup_adb_keyboards(task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error cleaning up keyboard: {str(e)}")
        
        # 断开ADB设备连接
        try:
            device_id = request.device.get("android", {}).get("url")
            if device_id:
                disconnect_device(device_id, task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error disconnecting device: {str(e)}")
        
        # 清理后台任务注册
        if task_id in background_tasks_registry:
            del background_tasks_registry[task_id]


@router.post("/reference-task/execute")
async def execute_reference_task(request: ReferenceTaskExecuteRequest):
    """
    执行参考任务
    
    Args:
        request: 参考任务执行请求
        
    Returns:
        任务创建结果，包含task_id
    """
    
    if const.NO_PROD_ENV == get_current_env():
        task_id = uuid.uuid4().hex
    else:
        task_id = request.task_id
    
    try:
        logger.info(f"[{task_id}] 📝 Creating reference task execution: {request.task_name}")
        logger.info(f"[{task_id}] 🔗 Reference task ID: {request.reference_task_id}")
        
        # 验证参考任务是否存在
        reference_task = reference_task_application.get_task_by_id(request.reference_task_id)
        if not reference_task:
            return {
                "code": 1,
                "message": f"Reference task {request.reference_task_id} not found",
                "data": {
                    "task_id": task_id
                }
            }
        
        # 创建任务记录
        task = reference_task_application.create_reference_task_from_request(task_id, request)
        if not task:
            return {
                "code": 1,
                "message": "Failed to create reference task",
                "data": {
                    "task_id": task_id
                }
            }
        
        # 启动后台执行线程
        thread = threading.Thread(
            target=execute_reference_task_background,
            args=(task_id, request),
            name=f"ReferenceTask-{task_id}"
        )
        thread.daemon = True
        thread.start()
        
        # 注册后台任务
        background_tasks_registry[task_id] = thread
        
        logger.info(f"[{task_id}] ✅ Reference task created and started in background")
        
        return {
            "code": 0,
            "message": "Reference task created successfully",
            "data": {
                "task_id": task_id,
                "reference_task_id": request.reference_task_id,
                "status": ExecutionStatus.PROCESSING.value
            }
        }
        
    except Exception as e:
        logger.error(f"[{task_id}] ❌ Failed to create reference task: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to create reference task: {str(e)}",
            "data": {
                "task_id": task_id
            }
        }



