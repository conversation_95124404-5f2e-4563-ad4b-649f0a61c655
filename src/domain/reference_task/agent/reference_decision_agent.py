#!/usr/bin/env python3
"""
参考任务决策执行Agent

融合决策和执行的Agent，直接调用vision模型输出坐标并执行Android操作
在prompt中包含参考任务的历史和图片
"""

import time
from typing import Dict, Any
from datetime import datetime

from loguru import logger
from openai import OpenAI

from config.globalconfig import get_or_create_settings_ins
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64
from src.domain.ui_task.mobile.android.action_tool import execute_simple_action
from src.domain.reference_task.repo.reference_state import ReferenceTaskState
from src.domain.reference_task.prompt.reference_decision_prompt import build_reference_decision_prompt


class ReferenceDecisionAgent:
    """参考任务决策执行Agent - 融合决策和执行"""

    def __init__(self):
        """初始化决策执行Agent"""
        config = get_or_create_settings_ins()
        self.client = OpenAI(
            base_url=config.paths.doubao_base_url,
            api_key=config.paths.doubao_api_key
        )
        self.model = config.paths.doubao_model

    def analyze_decide_and_execute(
            self, 
            state: ReferenceTaskState, 
            current_screenshot_path: str
    ) -> Dict[str, Any]:
        """
        基于参考任务分析当前界面、决策并执行动作
        
        Args:
            state: 参考任务状态
            current_screenshot_path: 当前截图路径
            
        Returns:
            执行结果
        """
        task_id = state["task_id"]
        device_id = state["device"]
        
        try:
            logger.info(f"[{task_id}] 🧠 Reference decision-execution agent analyzing...")
            
            # 转换当前截图为base64
            current_image_base64 = convert_screenshot_to_base64(current_screenshot_path, task_id)
            
            # 构建包含参考任务信息的prompt
            messages = self._build_reference_messages(state, current_image_base64)
            
            start_time = time.time()
            
            # 调用vision模型
            chat_completion = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0,
                max_tokens=4096,
                extra_body={
                    "thinking": {
                        "type": "disabled",  # 不使用深度思考能力
                    }
                },
            )
            
            model_response = chat_completion.choices[0].message.content
            end_time = time.time()
            
            logger.info(f"[{task_id}] 🧠 Reference model response time: {end_time - start_time:.2f}s")
            logger.info(f"[{task_id}] 🧠 Reference model response: \n{model_response}")
            
            # 解析模型响应并执行动作
            execution_result = self._parse_and_execute(model_response, device_id, task_id)
            
            # 记录执行结果到状态历史
            self._record_execution_result(state, model_response, execution_result)
            
            return execution_result
            
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Reference decision-execution agent failed: {str(e)}")
            return {
                "status": "error",
                "message": f"Agent failed: {str(e)}",
                "action": ""
            }

    def _build_reference_messages(
            self, 
            state: ReferenceTaskState, 
            current_image_base64: str
    ) -> list:
        """
        构建包含参考任务信息的消息列表
        
        Args:
            state: 参考任务状态
            current_image_base64: 当前截图base64
            
        Returns:
            消息列表
        """
        # 构建系统prompt，包含参考任务信息
        system_prompt = build_reference_decision_prompt(state)
        
        messages = [
            {
                "role": "system",
                "content": system_prompt
            }
        ]
        
        # 添加参考任务的历史截图（如果有的话）
        reference_images = self._get_reference_task_images(state["reference_task_id"])
        if reference_images:
            # 添加参考任务截图说明
            messages.append({
                "role": "user", 
                "content": "以下是参考任务执行过程中的关键截图，供你参考："
            })
            
            # 添加参考截图（最多3张，避免token过多）
            for i, img_base64 in enumerate(reference_images[:3]):
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"参考截图 {i+1}:"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{img_base64}"
                            }
                        }
                    ]
                })
        
        # 添加当前截图和执行指令
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": f"当前需要执行的任务: {state['task_description']}\n\n请分析当前界面截图，参考上述参考任务的执行过程，决策下一步的执行动作。请直接输出具体的坐标和动作命令："
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{current_image_base64}"
                    }
                }
            ]
        })
        
        return messages

    def _get_reference_task_images(self, reference_task_id: str) -> list:
        """
        获取参考任务的关键截图
        
        Args:
            reference_task_id: 参考任务ID
            
        Returns:
            截图base64列表
        """
        try:
            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)
            
            if not actions:
                return []
            
            images = []
            for action in actions:
                if hasattr(action, 'image_path') and action.image_path:
                    try:
                        # 转换截图为base64
                        img_base64 = convert_screenshot_to_base64(action.image_path, reference_task_id)
                        if img_base64:
                            images.append(img_base64)
                    except Exception as e:
                        logger.warning(f"Failed to load reference image {action.image_path}: {str(e)}")
                        continue
            
            logger.info(f"Loaded {len(images)} reference images for task {reference_task_id}")
            return images
            
        except Exception as e:
            logger.error(f"Error getting reference task images: {str(e)}")
            return []

    def _parse_and_execute(self, model_response: str, device_id: str, task_id: str) -> Dict[str, Any]:
        """
        解析模型响应并执行动作
        
        Args:
            model_response: 模型响应
            device_id: 设备ID
            task_id: 任务ID
            
        Returns:
            执行结果
        """
        try:
            # 解析动作命令和坐标
            action_info = self._parse_action_from_response(model_response)
            
            if not action_info:
                return {
                    "status": "error",
                    "message": "Failed to parse action from model response",
                    "action": ""
                }
            
            action_type = action_info.get("action_type")
            coordinates = action_info.get("coordinates")
            text = action_info.get("text")
            
            logger.info(f"[{task_id}] 🎯 Executing action: {action_type}, coordinates: {coordinates}")
            
            # 执行动作
            if action_type == "click" and coordinates:
                x, y = coordinates
                result = execute_simple_action(f"click {x} {y}", device_id)

            elif action_type == "scroll":
                direction = action_info.get("direction", "down")
                result = execute_simple_action(f"scroll {direction}", device_id)

            elif action_type == "type" and text:
                result = execute_simple_action(f"type {text}", device_id)

            elif action_type == "long_press" and coordinates:
                x, y = coordinates
                result = execute_simple_action(f"long_press {x} {y}", device_id)

            elif action_type == "wait":
                seconds = action_info.get("seconds", 3)
                time.sleep(seconds)
                result = {
                    "status": "success",
                    "action": f"wait({seconds})",
                    "message": f"Waited for {seconds} seconds"
                }

            elif action_type == "back":
                result = execute_simple_action("back", device_id)
            
            elif action_type == "finished":
                result = {
                    "status": "success",
                    "action": "finished",
                    "message": "Task completed successfully"
                }
            
            elif action_type == "failed":
                reason = action_info.get("reason", "Unknown error")
                result = {
                    "status": "failed",
                    "action": "failed",
                    "message": f"Task failed: {reason}"
                }
            
            else:
                result = {
                    "status": "error",
                    "action": action_type or "unknown",
                    "message": f"Unknown or invalid action: {action_type}"
                }
            
            return result
            
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error parsing and executing action: {str(e)}")
            return {
                "status": "error",
                "message": f"Execution error: {str(e)}",
                "action": ""
            }

    def _parse_action_from_response(self, model_response: str) -> Dict[str, Any]:
        """
        从模型响应中解析动作信息

        Args:
            model_response: 模型响应

        Returns:
            动作信息字典
        """
        try:
            lines = model_response.strip().split('\n')

            # 查找动作命令行
            action_line = ""
            for line in lines:
                line = line.strip()
                if line.startswith("动作命令：") or line.startswith("Action:") or line.startswith("动作:"):
                    action_line = line.split("：", 1)[-1].split(":", 1)[-1].strip()
                    break
                # 也检查是否直接包含动作关键词
                elif any(action in line.lower() for action in ['click(', 'scroll(', 'type(', 'wait(', 'back()', 'finished()', 'failed(']):
                    action_line = line
                    break

            if not action_line:
                return {}

            # 解析具体动作
            if action_line.startswith("click("):
                # 提取坐标 click(point='<point>x y</point>') 或 click(x, y)
                content = action_line[6:-1].strip()

                # 处理新格式 point='<point>x y</point>'
                if "point=" in content and "<point>" in content:
                    import re
                    point_match = re.search(r'<point>(\d+)\s+(\d+)</point>', content)
                    if point_match:
                        x = int(point_match.group(1))
                        y = int(point_match.group(2))
                        return {"action_type": "click", "coordinates": (x, y)}

                # 处理旧格式 click(x, y)
                elif "," in content and content.replace(",", "").replace(" ", "").isdigit():
                    coords = content.split(",")
                    if len(coords) == 2:
                        try:
                            x = int(coords[0].strip())
                            y = int(coords[1].strip())
                            return {"action_type": "click", "coordinates": (x, y)}
                        except ValueError:
                            pass

                # 无法解析坐标
                return {"action_type": "click", "coordinates": None, "element": content.strip('"\''), "error": "需要坐标"}

            elif action_line.startswith("scroll("):
                content = action_line[7:-1].strip()
                # 处理新格式 scroll(point='<point>x y</point>', direction='down')
                if "direction=" in content:
                    import re
                    direction_match = re.search(r"direction='([^']+)'", content)
                    if direction_match:
                        direction = direction_match.group(1)
                        return {"action_type": "scroll", "direction": direction}
                # 处理简单格式 scroll('down')
                else:
                    direction = content.strip('"\'')
                    return {"action_type": "scroll", "direction": direction}

            elif action_line.startswith("type("):
                content = action_line[5:-1].strip()
                # 处理新格式 type(content='text')
                if "content=" in content:
                    import re
                    content_match = re.search(r"content='([^']*)'", content)
                    if content_match:
                        text = content_match.group(1)
                        return {"action_type": "type", "text": text}
                # 处理简单格式 type('text')
                else:
                    text = content.strip('"\'')
                    return {"action_type": "type", "text": text}

            elif action_line.startswith("long_press("):
                content = action_line[11:-1].strip()
                # 处理新格式 long_press(point='<point>x y</point>')
                if "point=" in content and "<point>" in content:
                    import re
                    point_match = re.search(r'<point>(\d+)\s+(\d+)</point>', content)
                    if point_match:
                        x = int(point_match.group(1))
                        y = int(point_match.group(2))
                        return {"action_type": "long_press", "coordinates": (x, y)}
                # 处理旧格式 long_press(x, y)
                elif "," in content:
                    coords = content.split(",")
                    if len(coords) == 2:
                        try:
                            x = int(coords[0].strip())
                            y = int(coords[1].strip())
                            return {"action_type": "long_press", "coordinates": (x, y)}
                        except ValueError:
                            pass
                return {"action_type": "long_press", "coordinates": None, "error": "需要坐标"}

            elif action_line.startswith("wait("):
                seconds_str = action_line[5:-1].strip()
                try:
                    seconds = int(seconds_str)
                    return {"action_type": "wait", "seconds": seconds}
                except ValueError:
                    return {"action_type": "wait", "seconds": 3}

            elif action_line == "back()":
                return {"action_type": "back"}

            elif action_line == "finished()":
                return {"action_type": "finished"}

            elif action_line.startswith("failed("):
                content = action_line[7:-1].strip()
                # 处理新格式 failed(content='reason')
                if "content=" in content:
                    import re
                    content_match = re.search(r"content='([^']*)'", content)
                    if content_match:
                        reason = content_match.group(1)
                        return {"action_type": "failed", "reason": reason}
                # 处理简单格式 failed('reason')
                else:
                    reason = content.strip('"\'')
                    return {"action_type": "failed", "reason": reason}

            elif action_line.startswith("finished("):
                content = action_line[9:-1].strip()
                # 处理新格式 finished(content='message')
                if "content=" in content:
                    import re
                    content_match = re.search(r"content='([^']*)'", content)
                    if content_match:
                        message = content_match.group(1)
                        return {"action_type": "finished", "message": message}
                # 处理简单格式 finished('message') 或 finished()
                else:
                    message = content.strip('"\'') if content else "Task completed"
                    return {"action_type": "finished", "message": message}

            return {}

        except Exception as e:
            logger.error(f"Error parsing action from response: {str(e)}")
            return {}

    def _record_execution_result(
            self,
            state: ReferenceTaskState,
            model_response: str,
            execution_result: Dict[str, Any]
    ):
        """
        记录执行结果到状态历史

        Args:
            state: 参考任务状态
            model_response: 模型响应
            execution_result: 执行结果
        """
        try:
            history_entry = {
                "execution_count": state.get("execution_count", 0) + 1,
                "model_response": model_response[:500],  # 只保留前500字符
                "execution_result": execution_result,
                "timestamp": datetime.now().isoformat()
            }

            if "history" not in state:
                state["history"] = []

            state["history"].append(history_entry)

            # 只保留最近10条历史记录
            if len(state["history"]) > 10:
                state["history"] = state["history"][-10:]

        except Exception as e:
            logger.error(f"Error recording execution result: {str(e)}")
