#!/usr/bin/env python3
"""
参考任务执行服务

基于参考任务的动作列表，让模型参考执行新的用例
使用LangGraph控制执行流转，完全独立的实现
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from loguru import logger
from langgraph.graph import StateGraph, END

from src.domain.ui_task.mobile.repo import task_stop_manager
from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager
from src.domain.ui_task.mobile.aggregate.agent.supervisor_agent import SupervisorAgent
from src.domain.reference_task.agent.reference_decision_agent import ReferenceDecisionAgent
from src.domain.reference_task.repo.reference_state import ReferenceTaskState
from src.schema.action_types import ExecutionStatus


class ReferenceTaskService:
    """参考任务执行服务"""

    def __init__(self):
        """初始化服务"""
        self.decision_execution_agent = ReferenceDecisionAgent()
        self.supervisor_agent = SupervisorAgent()

    def execute_with_reference(
            self,
            task_id: str,
            task_description: str,
            reference_task_id: str,
            app_package: str,
            device_config: Dict[str, Any],
            agent_config_id: str = "tt",
            app_name: Optional[str] = None,
            app_description: Optional[str] = None,
            ui_component_instructions: Optional[str] = None,
            special_scenarios: Optional[str] = None,
            is_restart: bool = False
    ) -> Dict[str, Any]:
        """
        基于参考任务执行新的用例
        
        Args:
            task_id: 任务ID
            task_description: 要执行的测试用例描述
            reference_task_id: 参考任务ID
            app_package: 应用包名
            device_config: 设备配置
            agent_config_id: Agent配置ID
            app_name: 应用名称
            app_description: 应用描述
            ui_component_instructions: UI组件操作说明
            special_scenarios: 特殊场景处理说明
            is_restart: 是否重启应用
            
        Returns:
            执行结果
        """
        try:
            logger.info(f"[{task_id}] 🚀 Starting reference task execution...")
            
            # 获取参考任务的动作列表
            reference_actions = self._get_reference_task_actions(reference_task_id)
            if not reference_actions:
                return {
                    "success": False,
                    "message": f"No reference actions found for task {reference_task_id}",
                    "task_id": task_id
                }
            
            logger.info(f"[{task_id}] 📋 Found {len(reference_actions)} reference actions")
            
            # 初始化状态
            state = self._initialize_reference_state(
                task_id=task_id,
                task_description=task_description,
                reference_task_id=reference_task_id,
                reference_actions=reference_actions,
                app_package=app_package,
                device_config=device_config,
                agent_config_id=agent_config_id,
                app_name=app_name,
                app_description=app_description,
                ui_component_instructions=ui_component_instructions,
                special_scenarios=special_scenarios,
                is_restart=is_restart
            )
            
            # 创建执行图
            graph = self._create_execution_graph()
            
            # 在执行前更新任务状态为执行中
            task_persistence_service.update_task_status(
                task_id=task_id,
                status=ExecutionStatus.PROCESSING
            )
            
            # 执行图
            final_state = graph.invoke(state, {"recursion_limit": 200})
            
            # 生成结果
            result = self._generate_execution_result(final_state)
            
            logger.info(f"[{task_id}] ✅ Reference task execution completed")
            return result
            
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Reference task execution failed: {str(e)}")
            return {
                "success": False,
                "message": f"Reference task execution failed: {str(e)}",
                "task_id": task_id
            }

    def _get_reference_task_actions(self, reference_task_id: str) -> list:
        """
        获取参考任务的动作列表
        
        Args:
            reference_task_id: 参考任务ID
            
        Returns:
            动作列表
        """
        try:
            actions = task_persistence_service.get_task_actions(reference_task_id)
            if not actions:
                logger.warning(f"No actions found for reference task {reference_task_id}")
                return []
            
            # 转换为简化的动作描述列表，用于模型参考
            reference_actions = []
            for action in actions:
                action_info = {
                    "step_name": action.step_name,
                    "action": action.action,
                    "decision_content": action.decision_content,
                    "status": action.status
                }
                reference_actions.append(action_info)
            
            logger.info(f"Successfully retrieved {len(reference_actions)} reference actions")
            return reference_actions
            
        except Exception as e:
            logger.error(f"Error getting reference task actions: {str(e)}")
            return []

    def _initialize_reference_state(
            self,
            task_id: str,
            task_description: str,
            reference_task_id: str,
            reference_actions: list,
            app_package: str,
            device_config: Dict[str, Any],
            agent_config_id: str,
            app_name: Optional[str],
            app_description: Optional[str],
            ui_component_instructions: Optional[str],
            special_scenarios: Optional[str],
            is_restart: bool
    ) -> ReferenceTaskState:
        """
        初始化参考任务执行状态
        
        Returns:
            初始化的状态
        """
        state = ReferenceTaskState()

        # Task related (与DeploymentState保持一致)
        state["task"] = task_description  # User input task description
        state["task_id"] = task_id
        state["task_steps"] = []  # 将在后续填充
        state["completed"] = False
        state["execution_status"] = ExecutionStatus.PROCESSING.value
        state["retry_count"] = 0
        state["max_retries"] = 10
        state["step_failed"] = False
        state["error_message"] = None
        state["test_case_name"] = f"Reference Task Based on {reference_task_id}"
        state["test_case_description"] = task_description
        state["expected_result"] = ""
        state["app_package"] = app_package
        state["execution_count"] = 0
        state["is_restart"] = is_restart

        # Device related
        state["device"] = device_config.get("android", {}).get("url", "unknown")
        state["device_type"] = "android"
        state["device_config"] = device_config

        # Agent related
        state["agent_config_id"] = agent_config_id

        # Verification related
        state["verification_mode"] = "reference_based"
        state["step_expected_results"] = None
        state["current_step_index"] = 0
        state["verification_failure_reason"] = None
        state["execution_blocked"] = False
        state["block_reason"] = None

        # Prompt customization
        state["app_name"] = app_name
        state["app_description"] = app_description
        state["ui_component_instructions"] = ui_component_instructions
        state["special_scenarios"] = special_scenarios

        # Supervisor related
        state["supervisor_state"] = None

        # Page information
        state["current_page"] = {}

        # Records and messages
        state["history"] = []
        state["messages"] = []
        state["decision_fields"] = None

        # Callback
        state["callback"] = None

        # 参考任务特有字段
        state["reference_task_id"] = reference_task_id
        state["reference_actions"] = reference_actions

        # 添加初始化记录
        state["history"].append({
            "action": "reference_task_initialization",
            "task_description": task_description,
            "reference_task_id": reference_task_id,
            "reference_actions_count": len(reference_actions),
            "timestamp": datetime.now().isoformat()
        })

        return state

    def _create_execution_graph(self) -> StateGraph:
        """
        创建基于LangGraph的执行图
        
        Returns:
            编译后的执行图
        """
        def route_next_action(state: ReferenceTaskState) -> str:
            """路由下一个动作"""
            if state.get("completed", False):
                return "end"
            
            # 检查任务是否被停止
            if task_stop_manager.is_task_stopped(state["task_id"]):
                logger.info(f"[{state['task_id']}] 🛑 Task stopped by user, ending execution...")
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.TERMINATE.value
                return "end"
            
            # 检查失败重试逻辑
            if state.get("step_failed", False):
                retry_count = state.get("retry_count", 0)
                max_retries = state.get("max_retries", 10)
                if retry_count >= max_retries:
                    logger.info(f"[{state['task_id']}] ⏭️ Task failed after {max_retries} retries, ending execution...")
                    state["completed"] = True
                    state["execution_status"] = ExecutionStatus.FAILED.value
                    return "end"
            
            return "execute_step"
        
        # 创建图
        graph = StateGraph(ReferenceTaskState)
        
        # 添加节点
        graph.add_node("execute_step", self._execute_reference_step_node)
        graph.set_entry_point("execute_step")
        
        # 添加条件边
        graph.add_conditional_edges(
            "execute_step",
            route_next_action,
            {
                "execute_step": "execute_step",
                "end": END
            }
        )
        
        return graph.compile()

    def _execute_reference_step_node(self, state: ReferenceTaskState) -> ReferenceTaskState:
        """
        执行参考任务步骤节点
        
        Args:
            state: 当前状态
            
        Returns:
            更新后的状态
        """
        task_id = state["task_id"]
        
        try:
            logger.info(f"[{task_id}] 🎯 Executing reference-based step...")
            
            # 检查任务是否已完成
            if state.get("completed", False):
                return state
            
            # 拍摄当前界面截图
            screenshot_path = screenshot_manager.take_screenshot(task_id, state["device"])
            if not screenshot_path:
                logger.error(f"[{task_id}] ❌ Failed to take screenshot")
                state["step_failed"] = True
                state["retry_count"] = state.get("retry_count", 0) + 1
                return state

            # 使用融合的决策执行Agent
            execution_result = self.decision_execution_agent.analyze_decide_and_execute(state, screenshot_path)

            if not execution_result:
                logger.error(f"[{task_id}] ❌ No execution result")
                state["step_failed"] = True
                state["retry_count"] = state.get("retry_count", 0) + 1
                return state

            # 检查是否为完成动作
            action = execution_result.get("action", "")
            if action == "finished":
                logger.info(f"[{task_id}] ✅ Task completed with finished action")
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.SUCCEED.value
                return state
            elif action == "failed":
                logger.info(f"[{task_id}] ❌ Task failed with failed action")
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.FAILED.value
                return state
            
            # 更新状态
            state["execution_count"] = state.get("execution_count", 0) + 1
            state["step_failed"] = execution_result.get("status") != "success"
            
            if state["step_failed"]:
                state["retry_count"] = state.get("retry_count", 0) + 1
                logger.warning(f"[{task_id}] ⚠️ Step failed, retry count: {state['retry_count']}")
            else:
                state["retry_count"] = 0  # 重置重试计数
                logger.info(f"[{task_id}] ✅ Step executed successfully")
            
            return state
            
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error in reference step execution: {str(e)}")
            state["step_failed"] = True
            state["retry_count"] = state.get("retry_count", 0) + 1
            state["error_message"] = str(e)
            return state

    def _generate_execution_result(self, final_state: ReferenceTaskState) -> Dict[str, Any]:
        """
        生成执行结果
        
        Args:
            final_state: 最终状态
            
        Returns:
            执行结果
        """
        execution_status = final_state.get("execution_status", ExecutionStatus.FAILED.value)
        success = execution_status == ExecutionStatus.SUCCEED.value
        
        result = {
            "success": success,
            "task_id": final_state["task_id"],
            "execution_status": execution_status,
            "execution_count": final_state.get("execution_count", 0),
            "reference_task_id": final_state.get("reference_task_id"),
            "message": final_state.get("error_message", "Task completed" if success else "Task failed")
        }
        
        return result


# 创建全局实例
reference_task_service = ReferenceTaskService()
